version: '3.8'

services:
  app:
    # image: 448938462703.dkr.ecr.ap-southeast-1.amazonaws.com/ext/ki/invoice-matching:latest
    image: invoice-matching:latest
    container_name: invoice-matching
    ports:
      - "3000:3000"
    environment:
      # Node environment
      - NODE_ENV=production
      # Database connection
      - DATABASE_URL=postgresql://postgres.axyujusllpfbbgewabta:<EMAIL>:5432/invoice_matching_staging
      # NextAuth configuration
      - NEXTAUTH_SECRET=kSQlOu+drrdvlgRc/xr+k3UMR7Q+Pt80REBbScSRY8o=
      - NEXTAUTH_URL=http://localhost:3000
      # Document Processing Service
      - DONA_HOST=http://************:8000
      - DONA_API_KEY=m9mOq0v3Qtzx73iekKMLGjnNo7MpqGRN
      # - DONA_HOST=https://dona-api-staging.happyfresh.io
      # - DONA_API_KEY=m9mOq0v3Qtzx73iekKMLGjnNo7MpqGRN
      # Sentry
      - NEXT_PUBLIC_SENTRY_DSN="https://<EMAIL>/78"
      # New Relic Configuration
      - NEW_RELIC_LICENSE_KEY="9d42ad1cf88c792127d2c98a165b4e2aFFFFNRAL"
      - NEW_RELIC_APP_NAME="kim-dev"
      - NEW_RELIC_LOG_LEVEL="info"
      - NEW_RELIC_DISTRIBUTED_TRACING_ENABLED="true"
      - NEW_RELIC_TRACER_THRESHOLD="apdex_f"

    restart: unless-stopped
    # networks:
    #   - invoice-network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    volumes:
      - ./public:/app/public
      - ./uploads:/app/uploads

# networks:
#   invoice-network:
#     driver: bridge
