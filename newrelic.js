'use strict'

/**
 * New Relic agent configuration.
 *
 * See lib/config/default.js in the agent distribution for a more complete
 * description of configuration variables and their potential values.
 */
exports.config = {
  /**
   * Array of application names.
   */
  app_name: [process.env.NEW_RELIC_APP_NAME || 'Invoice Matching API'],

  /**
   * Your New Relic license key.
   */
  license_key: process.env.NEW_RELIC_LICENSE_KEY,

  /**
   * This setting controls distributed tracing.
   * Distributed tracing lets you see the path that a request takes through your
   * distributed system. Enabling distributed tracing changes the behavior of some
   * New Relic features, so carefully consult the transition guide before you enable
   * this feature: https://docs.newrelic.com/docs/transition-guide-distributed-tracing
   * Default is true.
   */
  distributed_tracing: {
    /**
     * Enables/disables distributed tracing.
     *
     * @env NEW_RELIC_DISTRIBUTED_TRACING_ENABLED
     */
    enabled: true
  },

  /**
   * Logging configuration
   */
  logging: {
    /**
     * Level at which to log. 'trace' is most useful to New Relic when diagnosing
     * issues with the agent, 'info' and higher will impose the least overhead on
     * production applications.
     */
    level: process.env.NEW_RELIC_LOG_LEVEL || 'info',

    /**
     * Where to put the log file -- by default just logs to stdout
     */
    filepath: process.env.NEW_RELIC_LOG_FILE || 'stdout',

    /**
     * Whether to write to a log file in addition to stdout
     */
    enabled: true
  },

  /**
   * When true, all request headers except for those listed in attributes.exclude
   * will be captured for all traces, unless otherwise specified in a destination's
   * attributes include/exclude lists.
   */
  allow_all_headers: true,

  /**
   * Attributes configuration
   */
  attributes: {
    /**
     * Prefix of attributes to exclude from all destinations. Allows * as wildcard
     * at end of string.
     */
    exclude: [
      'request.headers.cookie',
      'request.headers.authorization',
      'request.headers.x-api-key',
      'response.headers.set-cookie*'
    ]
  },

  /**
   * Transaction tracer configuration
   */
  transaction_tracer: {
    /**
     * Enable/disable transaction tracing. Enabled by default.
     */
    enabled: true,

    /**
     * Threshold in milliseconds. When the response time of a web transaction
     * exceeds this threshold, a transaction trace will be recorded.
     */
    transaction_threshold: process.env.NEW_RELIC_TRACER_THRESHOLD || 'apdex_f',

    /**
     * Maximum number of transaction trace nodes to record in a single transaction.
     */
    top_n: 20
  },

  /**
   * Error collector configuration
   */
  error_collector: {
    /**
     * Enable/disable error collection. Enabled by default.
     */
    enabled: true,

    /**
     * List of HTTP error status codes the error collector should ignore.
     */
    ignore_status_codes: [401, 404]
  },

  /**
   * Browser monitoring configuration
   */
  browser_monitoring: {
    /**
     * Enable/disable browser monitoring. Disabled by default for API-only applications.
     */
    enable: false
  },

  /**
   * Application logging configuration
   */
  application_logging: {
    /**
     * Enable/disable application logging features
     */
    enabled: true,

    /**
     * Enable/disable forwarding of application logs to New Relic
     */
    forwarding: {
      enabled: true,

      /**
       * Maximum number of log records to send per minute
       */
      max_samples_stored: 10000
    },

    /**
     * Enable/disable local log decoration with New Relic metadata
     */
    local_decorating: {
      enabled: true
    },

    /**
     * Enable/disable metrics based on application logs
     */
    metrics: {
      enabled: true
    }
  },

  /**
   * Rules for naming or ignoring transactions.
   */
  rules: {
    /**
     * A list of rules for naming transactions.
     */
    name: [
      // API endpoints
      { pattern: '/api/process-invoice', name: 'API/ProcessInvoice' },
      { pattern: '/api/process-tax-invoice', name: 'API/ProcessTaxInvoice' },
      { pattern: '/api/process-invoice-file', name: 'API/ProcessInvoiceFile' },
      { pattern: '/api/process-tax-invoice-file', name: 'API/ProcessTaxInvoiceFile' },
      { pattern: '/api/health', name: 'API/Health' },
      { pattern: '/api/admin/*', name: 'API/Admin' },
      { pattern: '/api/auth/*', name: 'API/Auth' },
      { pattern: '/api/logs', name: 'API/Logs' },
      // Admin pages
      { pattern: '/admin/*', name: 'Admin/Page' }
    ],

    /**
     * A list of patterns for transactions to ignore entirely.
     */
    ignore: [
      // Ignore health checks and monitoring endpoints
      '/api/health',
      // Ignore static assets
      '/_next/static/*',
      '/favicon.ico'
    ]
  },

  /**
   * Custom insights configuration
   */
  custom_insights_events: {
    /**
     * Enable/disable custom events
     */
    enabled: true,

    /**
     * Maximum number of custom events to buffer in memory
     */
    max_samples_stored: 30000
  },

  /**
   * Slow SQL configuration
   */
  slow_sql: {
    /**
     * Enable/disable slow SQL collection
     */
    enabled: true,

    /**
     * Threshold for slow SQL in milliseconds
     */
    max_samples: 10
  }
}
