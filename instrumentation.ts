import * as Sentry from '@sentry/nextjs';

export async function register() {
  if (process.env.NEXT_RUNTIME === 'nodejs') {
    // Initialize New Relic first, before any other modules
    if (process.env.NEW_RELIC_LICENSE_KEY) {
      try {
        await import('newrelic');
      } catch (error) {
        console.warn('Failed to initialize New Relic:', error);
      }
    }

    // Then initialize Sentry
    await import('./sentry.server.config');
  }

  if (process.env.NEXT_RUNTIME === 'edge') {
    await import('./sentry.edge.config');
  }
}

export const onRequestError = Sentry.captureRequestError;