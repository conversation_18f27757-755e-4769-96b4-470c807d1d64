# New Relic Quick Start Guide

## Prerequisites

1. **New Relic Account**: Sign up at [newrelic.com](https://newrelic.com) if you don't have an account
2. **License Key**: Obtain your New Relic license key from your account settings

## Quick Setup

### 1. Set Environment Variables

Update your `.env` file with your New Relic license key:

```bash
# Replace with your actual license key
NEW_RELIC_LICENSE_KEY="your_actual_license_key_here"
NEW_RELIC_APP_NAME="Invoice Matching API - Development"
```

### 2. Test the Integration

Start the development server:

```bash
pnpm dev
```

You should see New Relic initialization messages in the console:

```
{"v":0,"level":30,"name":"newrelic","msg":"Using New Relic for Node.js. Agent version: 12.19.0"}
{"v":0,"level":30,"name":"newrelic","msg":"Agent state changed from stopped to starting."}
{"v":0,"level":30,"name":"newrelic","msg":"Connected to collector.newrelic.com:443"}
```

### 3. Generate Some Traffic

Make API calls to generate data:

```bash
# Test the health endpoint
curl http://localhost:3000/api/health

# Test an invoice processing endpoint (requires API key)
curl -X POST http://localhost:3000/api/process-invoice \
  -H "Content-Type: application/json" \
  -H "X-API-Key: your_api_key" \
  -d '{
    "file_url": "https://example.com/invoice.pdf",
    "vendor_name": "Test Vendor",
    "invoice_number": "INV-001",
    "invoice_date": "2024-01-01",
    "invoice_amount": 100.00,
    "vat_amount": 10.00
  }'
```

### 4. View Data in New Relic

1. Log into your New Relic account
2. Navigate to **APM & Services**
3. Find your application: "Invoice Matching API - Development"
4. Explore the dashboards and metrics

## What You'll See

### Automatic Monitoring

- **Response Times**: Average and percentile response times for all endpoints
- **Throughput**: Requests per minute
- **Error Rates**: Percentage of failed requests
- **Database Queries**: Prisma/PostgreSQL query performance

### Custom Events

- **InvoiceProcessingStarted**: When invoice processing begins
- **InvoiceProcessingCompleted**: When processing completes successfully
- **InvoiceProcessingFailed**: When processing fails

### Custom Metrics

- **PDF Processing**: Page counts, color detection times
- **External API**: DPS service response times
- **Business Logic**: Processing success rates, field matching statistics

### Custom Attributes

Filter and analyze data by:
- `invoice.requestType` (invoice/taxInvoice)
- `invoice.hasApiKey` (true/false)
- `pdf.totalPages` (number)
- `pdf.isColored` (true/false)
- `external.DPS.responseTime` (milliseconds)

## Production Deployment

### Docker

The application is ready for Docker deployment with New Relic:

```bash
# Build the Docker image
docker build -t invoice-matching-api .

# Run with New Relic configuration
docker run -p 3000:3000 \
  -e NEW_RELIC_LICENSE_KEY="your_production_license_key" \
  -e NEW_RELIC_APP_NAME="Invoice Matching API - Production" \
  -e DATABASE_URL="your_production_database_url" \
  invoice-matching-api
```

### Environment Variables for Production

```bash
NEW_RELIC_LICENSE_KEY="your_production_license_key"
NEW_RELIC_APP_NAME="Invoice Matching API - Production"
NEW_RELIC_LOG_LEVEL="info"
NEW_RELIC_DISTRIBUTED_TRACING_ENABLED="true"
```

## Troubleshooting

### Common Issues

1. **No data appearing in New Relic**
   - Check that `NEW_RELIC_LICENSE_KEY` is set correctly
   - Verify the license key is valid
   - Check application logs for New Relic connection errors

2. **Build errors**
   - Ensure you're using Node.js 22 or compatible version
   - Clear Next.js cache: `rm -rf .next`
   - Reinstall dependencies: `rm -rf node_modules && pnpm install`

3. **Docker issues**
   - Ensure environment variables are passed to the container
   - Check that the newrelic.js file is copied to the container
   - Verify log output is going to stdout

### Debug Mode

Enable debug logging:

```bash
NEW_RELIC_LOG_LEVEL="trace"
```

### Verify Integration

Check that New Relic is working:

```bash
# Look for New Relic logs in application output
pnpm dev | grep newrelic

# Check if the agent is reporting
curl http://localhost:3000/api/health
# Then check New Relic dashboard for the transaction
```

## Next Steps

1. **Set up Alerts**: Configure alerts for high error rates, slow response times
2. **Create Dashboards**: Build custom dashboards for business metrics
3. **Monitor Trends**: Track performance over time
4. **Optimize**: Use insights to improve application performance

## Support

- **New Relic Documentation**: [docs.newrelic.com](https://docs.newrelic.com)
- **Node.js Agent Guide**: [docs.newrelic.com/docs/apm/agents/nodejs-agent](https://docs.newrelic.com/docs/apm/agents/nodejs-agent)
- **Next.js Integration**: [docs.newrelic.com/docs/apm/agents/nodejs-agent/getting-started/introduction-new-relic-nodejs](https://docs.newrelic.com/docs/apm/agents/nodejs-agent/getting-started/introduction-new-relic-nodejs)

## Key Benefits

✅ **Zero-downtime monitoring** - No application changes required for basic monitoring  
✅ **Comprehensive coverage** - API endpoints, database, external services  
✅ **Business insights** - Custom events and metrics for invoice processing  
✅ **Error tracking** - Automatic error capture and alerting  
✅ **Performance optimization** - Identify bottlenecks and slow queries  
✅ **Production ready** - Docker and environment-specific configurations
