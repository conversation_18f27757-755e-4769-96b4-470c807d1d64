{"name": "invoice-matching", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "db:generate": "prisma generate", "db:push": "prisma db push", "db:setup": "prisma generate && prisma db push", "test": "vitest", "test:run": "vitest run", "test:coverage": "vitest run --reporter verbose", "create-admin": "node scripts/create-admin.js", "reset-admin-password": "node scripts/reset-admin-password.js"}, "dependencies": {"@hookform/resolvers": "^3.10.0", "@prisma/client": "^6.8.2", "@radix-ui/react-accordion": "1.2.2", "@radix-ui/react-alert-dialog": "1.1.4", "@radix-ui/react-aspect-ratio": "1.1.1", "@radix-ui/react-avatar": "1.1.2", "@radix-ui/react-checkbox": "1.1.3", "@radix-ui/react-collapsible": "1.1.2", "@radix-ui/react-context-menu": "2.2.4", "@radix-ui/react-dialog": "1.1.4", "@radix-ui/react-dropdown-menu": "2.1.4", "@radix-ui/react-hover-card": "1.1.4", "@radix-ui/react-label": "2.1.1", "@radix-ui/react-menubar": "1.1.4", "@radix-ui/react-navigation-menu": "1.2.3", "@radix-ui/react-popover": "1.1.4", "@radix-ui/react-progress": "1.1.1", "@radix-ui/react-radio-group": "1.2.2", "@radix-ui/react-scroll-area": "1.2.2", "@radix-ui/react-select": "2.1.4", "@radix-ui/react-separator": "1.1.1", "@radix-ui/react-slider": "1.2.2", "@radix-ui/react-slot": "1.1.1", "@radix-ui/react-switch": "1.1.2", "@radix-ui/react-tabs": "1.1.2", "@radix-ui/react-toast": "1.2.4", "@radix-ui/react-toggle": "1.1.1", "@radix-ui/react-toggle-group": "1.1.1", "@radix-ui/react-tooltip": "1.1.6", "@sentry/nextjs": "^9.22.0", "@tanstack/react-table": "^8.21.3", "autoprefixer": "^10.4.21", "bcrypt": "^5.1.1", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "date-fns": "4.1.0", "embla-carousel-react": "8.5.1", "formidable": "^3.5.4", "input-otp": "1.4.1", "lucide-react": "^0.454.0", "newrelic": "^12.19.0", "next": "15.3.2", "next-auth": "^4.24.11", "next-swagger-doc": "^0.4.1", "next-themes": "^0.4.6", "pdfjs-dist": "^4.10.38", "pino": "^9.7.0", "prisma": "^6.8.2", "react": "^19.1.0", "react-day-picker": "8.10.1", "react-dom": "^19.1.0", "react-hook-form": "^7.56.4", "react-resizable-panels": "^2.1.9", "recharts": "latest", "sonner": "^1.7.4", "swagger-ui-react": "^5.22.0", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "vaul": "^0.9.9", "zod": "^3.25.28"}, "devDependencies": {"@testing-library/dom": "^9.3.4", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^15.0.7", "@testing-library/user-event": "^14.6.1", "@types/bcrypt": "^5.0.2", "@types/bcryptjs": "^3.0.0", "@types/formidable": "^3.4.5", "@types/node": "^22.15.21", "@types/react": "^19.1.5", "@types/react-dom": "^19.1.5", "@vitejs/plugin-react": "^4.5.0", "@vitest/coverage-v8": "^3.1.4", "jsdom": "^24.1.3", "msw": "^2.8.4", "pino-pretty": "^13.0.0", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "typescript": "^5.8.3", "vite-tsconfig-paths": "^4.3.2", "vitest": "^1.6.1"}, "packageManager": "pnpm@10.8.1+sha512.c50088ba998c67b8ca8c99df8a5e02fd2ae2e2b29aaf238feaa9e124248d3f48f9fb6db2424949ff901cffbb5e0f0cc1ad6aedb602cd29450751d11c35023677"}