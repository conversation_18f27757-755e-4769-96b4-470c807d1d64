import { pino, Logger } from 'pino';
import { AsyncLocalStorage } from 'async_hooks';
import { noticeError, addCustomAttributes } from './newrelic-helper';

// Define a type for the request context
interface RequestContext {
  requestId?: string;
  userId?: string;
  ipAddress?: string;
  userAgent?: string;
  path?: string;
  method?: string;
}

// Initialize AsyncLocalStorage for request-scoped context
export const asyncLocalStorage = new AsyncLocalStorage<RequestContext>();

// Determine if we are in a development environment
const isDev = process.env.NODE_ENV === 'development';

// Pino configuration for production (JSON output)
const productionConfig = {
  level: process.env.LOG_LEVEL || 'info',
  formatters: {
    level: (label: string) => ({ level: label.toUpperCase() }),
  },
  // Redact sensitive information
  redact: {
    paths: [
      'req.headers.authorization',
      'req.headers.cookie',
      'res.headers["set-cookie"]',
      'user.password',
      'data.password',
      'email',
      'address',
      'phone',
    ],
    censor: '[REDACTED]',
  },
  timestamp: () => `,"time":"${new Date(Date.now()).toISOString()}"`,
};

// Pino configuration for development (simplified to avoid worker issues)
const developmentConfig = {
  level: process.env.LOG_LEVEL || 'info',
  // Remove transport configuration to avoid worker thread issues
  // Use simple console output in development
  formatters: {
    level: (label: string) => ({ level: label.toUpperCase() }),
  },
  // Redact sensitive information in dev as well
  redact: {
    paths: [
      'req.headers.authorization',
      'req.headers.cookie',
      'res.headers["set-cookie"]',
      'user.password',
      'data.password',
      'email',
      'address',
      'phone',
    ],
    censor: '[REDACTED]',
  },
  timestamp: () => `,"time":"${new Date(Date.now()).toISOString()}"`,
};

// Create the base logger instance
const baseLogger = pino(isDev ? developmentConfig : productionConfig);

// Create a wrapper function for error logging that integrates with New Relic
function createErrorLogger(originalLogger: Logger) {
  return function(obj: any, msg?: string, ...args: any[]) {
    // Call the original error logger
    const result = originalLogger.error(obj, msg, ...args);

    // If obj is an Error instance, report it to New Relic
    if (obj instanceof Error) {
      const store = asyncLocalStorage.getStore();
      const customAttributes = store ? { ...store } : {};
      noticeError(obj, customAttributes);
    } else if (typeof obj === 'object' && obj.error instanceof Error) {
      // Handle cases where error is nested in the log object
      const store = asyncLocalStorage.getStore();
      const customAttributes = store ? { ...store } : {};
      noticeError(obj.error, { ...customAttributes, ...obj });
    }

    return result;
  };
}

// Extend the base logger to include request-scoped context and New Relic integration
const logger = new Proxy(baseLogger, {
  get(target, property, receiver) {
    const store = asyncLocalStorage.getStore();
    const childLogger = store ? target.child(store) : target;

    // Add request context to New Relic if available
    if (store) {
      addCustomAttributes({
        'request.id': store.requestId || 'unknown',
        'request.path': store.path || 'unknown',
        'request.method': store.method || 'unknown',
        'request.userAgent': store.userAgent || 'unknown'
      });
    }

    // If the property is the error method, return our custom error logger
    if (property === 'error') {
      return createErrorLogger(childLogger);
    }

    // If the property is a logging method (e.g., 'info', 'warn'), bind it to the childLogger
    if (typeof childLogger[property as keyof Logger] === 'function') {
      return (childLogger[property as keyof Logger] as Function).bind(childLogger);
    }
    // Otherwise, return the property directly from the childLogger
    return Reflect.get(childLogger, property, receiver);
  },
}) as Logger; // Cast to Logger to maintain type safety

export default logger;

// Type definitions for custom logger properties (if any)
declare module 'pino' {
  interface PinoLogger {
    withContext(context: RequestContext): Logger;
  }
}