/**
 * New Relic helper utilities for the Invoice Matching API
 *
 * This module provides utilities to integrate New Relic monitoring
 * with the existing application structure while maintaining compatibility
 * with the current API authentication and logging systems.
 */

let newrelic: any = null;

// Safely import New Relic only if it's available and configured
try {
  if (process.env.NEW_RELIC_LICENSE_KEY && typeof window === 'undefined' && process.env.NEXT_RUNTIME === 'nodejs') {
    newrelic = require('newrelic');
  }
} catch (error) {
  // New Relic not available or not configured - this is expected in some environments
  if (process.env.NODE_ENV === 'development') {
    console.warn('New Relic not available:', error);
  }
}

/**
 * Check if New Relic is available and configured
 */
export function isNewRelicAvailable(): boolean {
  return newrelic !== null;
}

/**
 * Add custom attributes to the current transaction
 * @param attributes Object containing key-value pairs to add as attributes
 */
export function addCustomAttributes(attributes: Record<string, string | number | boolean>): void {
  if (!isNewRelicAvailable()) return;

  try {
    for (const [key, value] of Object.entries(attributes)) {
      newrelic.addCustomAttribute(key, value);
    }
  } catch (error) {
    console.warn('Failed to add New Relic custom attributes:', error);
  }
}

/**
 * Record a custom event in New Relic Insights
 * @param eventType The type of event to record
 * @param attributes Object containing event attributes
 */
export function recordCustomEvent(eventType: string, attributes: Record<string, any>): void {
  if (!isNewRelicAvailable()) return;

  try {
    newrelic.recordCustomEvent(eventType, attributes);
  } catch (error) {
    console.warn('Failed to record New Relic custom event:', error);
  }
}

/**
 * Record an error in New Relic
 * @param error The error to record
 * @param customAttributes Optional custom attributes to include
 */
export function noticeError(error: Error, customAttributes?: Record<string, any>): void {
  if (!isNewRelicAvailable()) return;

  try {
    newrelic.noticeError(error, customAttributes);
  } catch (err) {
    console.warn('Failed to record error in New Relic:', err);
  }
}

/**
 * Set the name of the current transaction
 * @param name The transaction name
 */
export function setTransactionName(name: string): void {
  if (!isNewRelicAvailable()) return;

  try {
    newrelic.setTransactionName('Custom', name);
  } catch (error) {
    console.warn('Failed to set New Relic transaction name:', error);
  }
}

/**
 * Start timing a custom operation
 * @param name The name of the operation
 * @returns A function to call when the operation completes
 */
export function startTiming(name: string): () => void {
  if (!isNewRelicAvailable()) {
    return () => {}; // Return no-op function
  }

  try {
    const startTime = Date.now();

    return () => {
      const duration = Date.now() - startTime;
      newrelic.recordMetric(`Custom/${name}`, duration);
    };
  } catch (error) {
    console.warn('Failed to start New Relic timing:', error);
    return () => {};
  }
}

/**
 * Record a metric value in New Relic
 * @param name The metric name
 * @param value The metric value
 */
export function recordMetric(name: string, value: number): void {
  if (!isNewRelicAvailable()) return;

  try {
    newrelic.recordMetric(name, value);
  } catch (error) {
    console.warn('Failed to record New Relic metric:', error);
  }
}

/**
 * Add invoice processing specific attributes to New Relic
 * @param requestType Type of invoice processing request
 * @param apiKeyId API key ID if available
 * @param fileSize File size in bytes (for file uploads)
 * @param processingTime Processing time in milliseconds
 */
export function addInvoiceProcessingAttributes(
  requestType: 'invoice' | 'taxInvoice',
  apiKeyId?: string,
  fileSize?: number,
  processingTime?: number
): void {
  if (!isNewRelicAvailable()) return;

  const attributes: Record<string, string | number | boolean> = {
    'invoice.requestType': requestType,
    'invoice.hasApiKey': !!apiKeyId,
  };

  if (apiKeyId) {
    attributes['invoice.apiKeyId'] = apiKeyId;
  }

  if (fileSize !== undefined) {
    attributes['invoice.fileSize'] = fileSize;
  }

  if (processingTime !== undefined) {
    attributes['invoice.processingTime'] = processingTime;
  }

  addCustomAttributes(attributes);
}

/**
 * Record an invoice processing event
 * @param eventType The type of event (e.g., 'InvoiceProcessingStarted', 'InvoiceProcessingCompleted')
 * @param attributes Event-specific attributes
 */
export function recordInvoiceEvent(eventType: string, attributes: Record<string, any>): void {
  if (!isNewRelicAvailable()) return;

  recordCustomEvent(eventType, {
    ...attributes,
    timestamp: new Date().toISOString(),
    service: 'invoice-matching-api'
  });
}

/**
 * Record PDF processing metrics
 * @param totalPages Total number of pages in the PDF
 * @param colorPages Number of colored pages
 * @param isColored Whether the PDF contains color
 * @param processingTime Time taken for color detection
 */
export function recordPdfProcessingMetrics(
  totalPages: number,
  colorPages: number,
  isColored: boolean,
  processingTime: number
): void {
  if (!isNewRelicAvailable()) return;

  addCustomAttributes({
    'pdf.totalPages': totalPages,
    'pdf.colorPages': colorPages,
    'pdf.isColored': isColored,
    'pdf.colorDetectionTime': processingTime
  });

  recordMetric('Custom/PDF/TotalPages', totalPages);
  recordMetric('Custom/PDF/ColorPages', colorPages);
  recordMetric('Custom/PDF/ColorDetectionTime', processingTime);
}

/**
 * Record external API call metrics
 * @param apiName Name of the external API (e.g., 'DPS')
 * @param endpoint The API endpoint called
 * @param responseTime Response time in milliseconds
 * @param success Whether the call was successful
 */
export function recordExternalApiMetrics(
  apiName: string,
  endpoint: string,
  responseTime: number,
  success: boolean
): void {
  if (!isNewRelicAvailable()) return;

  addCustomAttributes({
    [`external.${apiName}.endpoint`]: endpoint,
    [`external.${apiName}.responseTime`]: responseTime,
    [`external.${apiName}.success`]: success
  });

  recordMetric(`Custom/External/${apiName}/ResponseTime`, responseTime);
  recordMetric(`Custom/External/${apiName}/${success ? 'Success' : 'Error'}`, 1);
}
