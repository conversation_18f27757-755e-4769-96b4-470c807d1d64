# New Relic Integration for Invoice Matching API

This document describes the New Relic Node.js agent integration for the Invoice Matching API application.

## Overview

The New Relic integration provides comprehensive monitoring and observability for the Invoice Matching API, including:

- Application Performance Monitoring (APM)
- Error tracking and alerting
- Custom metrics and events
- Log forwarding and correlation
- Distributed tracing
- Database query monitoring

## Configuration

### Environment Variables

The following environment variables need to be configured:

```bash
# Required
NEW_RELIC_LICENSE_KEY="your_license_key_here"

# Optional (with defaults)
NEW_RELIC_APP_NAME="Invoice Matching API - Development"
NEW_RELIC_LOG_LEVEL="info"
NEW_RELIC_DISTRIBUTED_TRACING_ENABLED="true"
NEW_RELIC_TRACER_THRESHOLD="apdex_f"
NEW_RELIC_LOG_FILE="stdout"
```

### Files Added/Modified

The following files have been added or modified for New Relic integration:

1. **newrelic.js** - New Relic configuration file
2. **lib/newrelic-helper.ts** - Helper utilities for New Relic integration
3. **instrumentation.ts** - Updated to initialize New Relic before other modules
4. **next.config.mjs** - Updated with New Relic webpack configuration
5. **lib/logger.ts** - Enhanced to report errors to New Relic
6. **app/api/process-invoice/route.ts** - Enhanced with New Relic monitoring
7. **Dockerfile** - Updated to include New Relic configuration
8. **.env** - Added New Relic environment variables

### Development Environment

1. Copy the `.env` file and update the New Relic configuration:
   ```bash
   cp .env .env.local
   # Edit .env.local with your New Relic license key
   ```

2. Set your New Relic license key:
   ```bash
   NEW_RELIC_LICENSE_KEY="your_actual_license_key"
   NEW_RELIC_APP_NAME="Invoice Matching API - Development"
   ```

### Production Environment

For production deployments, set the environment variables in your deployment configuration:

```bash
NEW_RELIC_LICENSE_KEY="your_production_license_key"
NEW_RELIC_APP_NAME="Invoice Matching API - Production"
NEW_RELIC_LOG_LEVEL="info"
```

## Docker Configuration

The Dockerfile has been updated to support New Relic:

1. **New Relic configuration file** (`newrelic.js`) is copied to the container
2. **Environment variables** are set for log forwarding to stdout
3. **Log forwarding** is configured to capture all application logs

### Docker Environment Variables

```dockerfile
ENV NEW_RELIC_LOG_FILE=stdout
ENV NEW_RELIC_DISTRIBUTED_TRACING_ENABLED=true
ENV NEW_RELIC_LOG_LEVEL=info
```

## Features

### 1. Transaction Monitoring

All API endpoints are automatically monitored with custom transaction names:

- `/api/process-invoice` → `API/ProcessInvoice`
- `/api/process-tax-invoice` → `API/ProcessTaxInvoice`
- `/api/process-invoice-file` → `API/ProcessInvoiceFile`
- `/api/process-tax-invoice-file` → `API/ProcessTaxInvoiceFile`
- `/api/health` → `API/Health`
- `/admin/*` → `Admin/Page`

### 2. Custom Events

The application records custom events for business logic tracking:

- `InvoiceProcessingStarted` - When invoice processing begins
- `InvoiceProcessingCompleted` - When processing completes successfully
- `InvoiceProcessingFailed` - When processing fails

### 3. Custom Metrics

Performance metrics are tracked for:

- **PDF Processing**: Page count, color detection time, colored pages
- **External API Calls**: Response times, success/failure rates
- **Processing Times**: Total request time, color detection time, external API time

### 4. Custom Attributes

Request-level attributes are added for filtering and analysis:

- `invoice.requestType` - Type of invoice (invoice/taxInvoice)
- `invoice.hasApiKey` - Whether request used API key authentication
- `invoice.apiKeyId` - API key identifier (if applicable)
- `invoice.fileSize` - File size for upload requests
- `pdf.totalPages` - Number of pages in PDF
- `pdf.isColored` - Whether PDF contains color
- `external.DPS.responseTime` - External service response time

### 5. Error Tracking

All errors are automatically captured and sent to New Relic with:

- Full stack traces
- Request context information
- Custom attributes for debugging

### 6. Log Forwarding

Application logs are forwarded to New Relic with:

- Structured JSON format
- Request correlation
- Sensitive data redaction
- Log level filtering

## Integration Points

### API Endpoints

The main API endpoints have been enhanced with New Relic monitoring:

1. **Request timing** - Total processing time
2. **External API monitoring** - DPS service calls
3. **PDF processing metrics** - Color detection and page counting
4. **Business events** - Processing lifecycle events

### Database Monitoring

Prisma database queries are automatically monitored through the New Relic agent, providing:

- Query performance metrics
- Slow query detection
- Database connection monitoring

### Error Handling

The existing logger has been enhanced to automatically report errors to New Relic while maintaining compatibility with the current logging structure.

## Monitoring Dashboards

### Key Metrics to Monitor

1. **Response Times**
   - Average response time per endpoint
   - 95th percentile response times
   - External API response times

2. **Error Rates**
   - Overall error rate
   - Error rate by endpoint
   - External API error rates

3. **Throughput**
   - Requests per minute
   - Processing volume by type
   - PDF processing statistics

4. **Business Metrics**
   - Invoice processing success rate
   - Average pages per document
   - Color detection accuracy

### Alerts

Consider setting up alerts for:

- High error rates (>5%)
- Slow response times (>30 seconds)
- External API failures
- High memory usage
- Database connection issues

## Troubleshooting

### Common Issues

1. **New Relic not reporting data**
   - Verify `NEW_RELIC_LICENSE_KEY` is set correctly
   - Check application logs for New Relic initialization errors
   - Ensure `instrumentationHook` is enabled in Next.js config

2. **Missing transaction data**
   - Verify the instrumentation.ts file is being loaded
   - Check that New Relic is imported before other modules

3. **Log forwarding not working**
   - Ensure `NEW_RELIC_LOG_FILE=stdout` in Docker
   - Verify application logging configuration

### Debug Mode

To enable debug logging for New Relic:

```bash
NEW_RELIC_LOG_LEVEL="trace"
```

## Security Considerations

1. **Sensitive Data Redaction**: The configuration automatically redacts sensitive headers and data
2. **API Key Protection**: API keys are excluded from New Relic attributes
3. **Environment Separation**: Use different app names for different environments

## Performance Impact

The New Relic agent has minimal performance impact:

- <1% CPU overhead
- <10MB memory overhead
- Asynchronous data transmission
- Configurable sampling rates

## Next Steps

1. **Set up New Relic account** and obtain license key
2. **Configure environment variables** for your deployment
3. **Create custom dashboards** for business metrics
4. **Set up alerting** for critical issues
5. **Review and tune** monitoring configuration based on usage patterns
