import { NextRequest, NextResponse } from 'next/server';
import { TaxInvoiceData } from '@/lib/types/invoice';
import { processTaxInvoiceFile } from '@/lib/api/external-service';
import { validateApi<PERSON>ey, ApiKeyValidationResult } from '@/lib/api/api-key-middleware';
import {
  createTaxInvoiceErrorResponse,
  createProcessingLog,
  updateTaxInvoiceLogWithResults,
  updateLogWithError,
  createApiResponse,
  extractTotalPages,
  getTotalPagesFromBuffer,
  updateLogWithApiKeyId,
  parseFormDataWithFormidable
} from '@/lib/api/shared-invoice-processing';
import logger from '@/lib/logger';

// Define a type for the file from FormData
interface FormDataFile extends File {
  arrayBuffer(): Promise<ArrayBuffer>;
}

// Use Node.js runtime for stream and fs support
export const runtime = 'nodejs';

// Disable body parser to handle file uploads
export const config = {
  api: {
    bodyParser: false,
  },
};

/**
 * Process and match a tax invoice file upload
 */
export async function POST(req: NextRequest): Promise<NextResponse> {
  // Validate API key
  const validation = await validateApiKey(req);

  // Handle API key validation
  if (validation && 'error' in validation && validation.error) {
    // Return error response if validation failed
    return validation.error;
  }
  // If validation is null or doesn't have an error, continue processing

  // New behavior - valid with API key ID or empty object for session auth

  try {
    // Use Formidable to parse multipart form data
    const { fields, files } = await parseFormDataWithFormidable(req);
    const pdfFile = files['file'];

    // Check if file is provided
    if (!pdfFile) {
      return NextResponse.json(
        {
          error: 'Missing PDF file',
          results: { success: false }
        },
        { status: 400 }
      );
    }

    // Validate required parameters using shared utility
    const requiredFields: Array<{ key: string; name: string; type: 'string' | 'number' | 'date' }> = [
      { key: 'vendor_name', name: 'vendor_name', type: 'string' },
      { key: 'tax_invoice_number', name: 'tax_invoice_number', type: 'string' },
      { key: 'tax_invoice_date', name: 'tax_invoice_date', type: 'date' },
      { key: 'invoice_amount', name: 'invoice_amount', type: 'number' },
      { key: 'vat_amount', name: 'vat_amount', type: 'number' },
    ];
    const { validateInvoiceFields } = await import('@/lib/api/validate-invoice-fields');
    const formValues: Record<string, unknown> = {};
    for (const field of requiredFields) {
      const value = fields[field.key];
      if (field.type === 'number' && typeof value === 'string') {
        formValues[field.key] = parseFloat(value);
      } else {
        formValues[field.key] = value;
      }
    }
    const validationError = validateInvoiceFields(formValues, requiredFields);
    if (validationError) {
      return NextResponse.json(
        {
          error: validationError,
          results: { success: false }
        },
        { status: 400 }
      );
    }

    // Extract input data from parsed fields
    const inputData: TaxInvoiceData = {
      vendorName: fields['vendor_name'] || undefined,
      taxInvoiceNo: fields['tax_invoice_number'] || undefined,
      invoiceDate: fields['tax_invoice_date'] || undefined,
      invoiceAmount: fields['invoice_amount'] ?
        parseFloat(fields['invoice_amount']) : undefined,
      vatAmount: fields['vat_amount'] ?
        parseFloat(fields['vat_amount']) : undefined,
    };

    // Use the buffer from the parsed file
    const buffer = pdfFile.buffer;

    // Create a log entry for this request
    const log = await createProcessingLog(
      'taxInvoice',
      pdfFile.filename,
      buffer.length,
      inputData
    );

    // If we have an API key ID, update the log with it
    if (validation && 'apiKeyId' in validation && validation.apiKeyId) {
      await updateLogWithApiKeyId(log.id, validation.apiKeyId);
    }

    try {
      // Start processing the tax invoice and page count in parallel
      const externalResponsePromise = processTaxInvoiceFile(buffer, inputData);
      const pageCountPromise = getTotalPagesFromBuffer(buffer);

      // Wait for the tax invoice processing to complete
      const externalResponse = await externalResponsePromise;

      // Use shared matching utility for robust field comparison
      const { matchFields } = await import('@/lib/api/field-matching');
      const fieldTypes: Record<string, "string" | "number" | "date"> = {
        vendor_name: 'string',
        tax_invoice_number: 'string',
        tax_invoice_date: 'date',
        invoice_amount: 'number',
        vat_amount: 'number',
      };
      const { fields: enhancedFields, summary: enhancedSummary } = matchFields(
        {
          vendor_name: inputData.vendorName,
          tax_invoice_number: inputData.taxInvoiceNo,
          tax_invoice_date: inputData.invoiceDate,
          invoice_amount: inputData.invoiceAmount,
          vat_amount: inputData.vatAmount,
        } as Record<string, string | number | Date>,
        externalResponse.results.fields,
        fieldTypes
      );
      // Get the page count from our PDF utility
      const pageCount = await pageCountPromise;
      // Use our page count, or fall back to DONA's total_pages if our count fails
      const totalPages = pageCount > 0 ? pageCount : extractTotalPages(externalResponse);
      // Add total_pages to the results
      const enhancedResults = {
        ...externalResponse.results,
        fields: enhancedFields,
        summary: enhancedSummary,
        total_pages: totalPages
      };

      // Update the log with the results
      await updateTaxInvoiceLogWithResults(log.id, externalResponse, enhancedResults);

      // Return the response in the format expected by the client
      return NextResponse.json(createApiResponse(externalResponse, log.id, enhancedResults));

    } catch (error) {
      // Update the log with the error
      await updateLogWithError(log.id, error);

      // Return error response
      return NextResponse.json(
        createTaxInvoiceErrorResponse(error, log.id),
        { status: 500 }
      );
    }

  } catch (error) {
    if (process.env.NODE_ENV !== 'test') {
      logger.error('Error processing tax invoice file:', error);
    }
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return NextResponse.json(
      {
        error: errorMessage,
        results: { success: false }
      },
      { status: 500 }
    );
  }
}
